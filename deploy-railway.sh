#!/bin/bash

# Railway Deployment Script for PrepLocus
# This script deploys both the main Next.js app and Socket.io server

set -e

echo "🚀 Starting Railway deployment for PrepLocus..."

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Check if user is logged in
if ! railway whoami &> /dev/null; then
    echo "❌ Not logged in to Railway. Please run: railway login"
    exit 1
fi

echo "✅ Railway CLI found and user authenticated"

# Deploy main Next.js application
echo "📦 Deploying main Next.js application..."
railway service nextjs-app
railway up --detach

echo "⏳ Waiting for main app deployment to complete..."
sleep 30

# Deploy Socket.io server
echo "🔌 Deploying Socket.io server..."
railway service socket-server
railway up --config railway-socket.json --detach

echo "⏳ Waiting for socket server deployment to complete..."
sleep 30

# Run database migrations
echo "🗄️ Running database migrations..."
railway service nextjs-app
railway run npx prisma migrate deploy

echo "✅ Deployment completed successfully!"
echo ""
echo "🌐 Your applications are now live:"
echo "Main App: $(railway service nextjs-app && railway domain)"
echo "Socket Server: $(railway service socket-server && railway domain)"
echo ""
echo "🔍 Check deployment status:"
echo "railway service nextjs-app && railway logs"
echo "railway service socket-server && railway logs"
