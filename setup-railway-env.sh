#!/bin/bash

# Railway Environment Setup Script
# Run this script to set up environment variables for both services

set -e

echo "🔧 Setting up Railway environment variables..."

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    read -p "$prompt [$default]: " input
    eval "$var_name=\"${input:-$default}\""
}

# Function to prompt for sensitive input (hidden)
prompt_sensitive() {
    local prompt="$1"
    local var_name="$2"
    
    read -s -p "$prompt: " input
    echo
    eval "$var_name=\"$input\""
}

echo "📝 Please provide the following information:"
echo ""

# Core configuration
prompt_with_default "Main app domain (without https://)" "your-app.railway.app" MAIN_DOMAIN
prompt_with_default "Socket server domain (without wss://)" "your-socket.railway.app" SOCKET_DOMAIN

# Database (should be auto-configured by Railway)
echo "ℹ️  Database URL will be auto-configured by Railway PostgreSQL service"

# Auth configuration
echo ""
echo "🔐 Authentication Configuration:"
prompt_sensitive "NextAuth Secret (or press Enter to generate)" NEXTAUTH_SECRET
if [ -z "$NEXTAUTH_SECRET" ]; then
    NEXTAUTH_SECRET=$(openssl rand -base64 32)
    echo "Generated NextAuth Secret: $NEXTAUTH_SECRET"
fi

prompt_with_default "Google Client ID" "" GOOGLE_CLIENT_ID
prompt_sensitive "Google Client Secret" GOOGLE_CLIENT_SECRET
prompt_with_default "GitHub Client ID" "" GITHUB_CLIENT_ID
prompt_sensitive "GitHub Client Secret" GITHUB_CLIENT_SECRET

# Email configuration
echo ""
echo "📧 Email Configuration:"
prompt_with_default "SMTP Host" "smtp.gmail.com" SMTP_HOST
prompt_with_default "SMTP Port" "587" SMTP_PORT
prompt_with_default "Email User" "" EMAIL_USER
prompt_sensitive "Email Password/App Password" EMAIL_PASS
prompt_with_default "Contact Email" "$EMAIL_USER" CONTACT_EMAIL

# Payment configuration
echo ""
echo "💳 Payment Configuration (Razorpay):"
prompt_with_default "Razorpay Key ID" "" RAZORPAY_KEY_ID
prompt_sensitive "Razorpay Key Secret" RAZORPAY_KEY_SECRET
prompt_sensitive "Razorpay Webhook Secret" RAZORPAY_WEBHOOK_SECRET

# CDN configuration
echo ""
echo "🌐 Bunny CDN Configuration:"
prompt_with_default "Storage Zone Name" "" BUNNY_STORAGE_ZONE_NAME
prompt_sensitive "Storage Access Key" BUNNY_STORAGE_ACCESS_KEY
prompt_with_default "Pull Zone URL" "https://your-zone.b-cdn.net" BUNNY_PULL_ZONE_URL
prompt_with_default "Storage Region" "storage" BUNNY_STORAGE_REGION

# AI API Keys (optional)
echo ""
echo "🤖 AI API Keys (Optional):"
prompt_sensitive "OpenAI API Key (optional)" OPENAI_API_KEY
prompt_sensitive "Anthropic API Key (optional)" ANTHROPIC_API_KEY
prompt_sensitive "Google AI API Key (optional)" GOOGLE_AI_API_KEY

echo ""
echo "🚀 Setting up main Next.js application environment..."

# Set main app variables
railway service use web

railway variables set NODE_ENV production
railway variables set NEXTAUTH_URL "https://$MAIN_DOMAIN"
railway variables set NEXTAUTH_SECRET "$NEXTAUTH_SECRET"
railway variables set NEXT_PUBLIC_SOCKET_ENABLED true
railway variables set NEXT_PUBLIC_SOCKET_URL "wss://$SOCKET_DOMAIN"

if [ ! -z "$GOOGLE_CLIENT_ID" ]; then
    railway variables set GOOGLE_CLIENT_ID "$GOOGLE_CLIENT_ID"
    railway variables set GOOGLE_CLIENT_SECRET "$GOOGLE_CLIENT_SECRET"
fi

if [ ! -z "$GITHUB_CLIENT_ID" ]; then
    railway variables set GITHUB_CLIENT_ID "$GITHUB_CLIENT_ID"
    railway variables set GITHUB_CLIENT_SECRET "$GITHUB_CLIENT_SECRET"
fi

if [ ! -z "$EMAIL_USER" ]; then
    railway variables set SMTP_HOST "$SMTP_HOST"
    railway variables set SMTP_PORT "$SMTP_PORT"
    railway variables set EMAIL_USER "$EMAIL_USER"
    railway variables set EMAIL_PASS "$EMAIL_PASS"
    railway variables set CONTACT_EMAIL "$CONTACT_EMAIL"
fi

if [ ! -z "$RAZORPAY_KEY_ID" ]; then
    railway variables set RAZORPAY_KEY_ID "$RAZORPAY_KEY_ID"
    railway variables set RAZORPAY_KEY_SECRET "$RAZORPAY_KEY_SECRET"
    railway variables set NEXT_PUBLIC_RAZORPAY_KEY_ID "$RAZORPAY_KEY_ID"
    railway variables set RAZORPAY_WEBHOOK_SECRET "$RAZORPAY_WEBHOOK_SECRET"
fi

if [ ! -z "$BUNNY_STORAGE_ZONE_NAME" ]; then
    railway variables set BUNNY_STORAGE_ZONE_NAME "$BUNNY_STORAGE_ZONE_NAME"
    railway variables set BUNNY_STORAGE_ACCESS_KEY "$BUNNY_STORAGE_ACCESS_KEY"
    railway variables set BUNNY_PULL_ZONE_URL "$BUNNY_PULL_ZONE_URL"
    railway variables set BUNNY_STORAGE_REGION "$BUNNY_STORAGE_REGION"
fi

if [ ! -z "$OPENAI_API_KEY" ]; then
    railway variables set OPENAI_API_KEY "$OPENAI_API_KEY"
fi

if [ ! -z "$ANTHROPIC_API_KEY" ]; then
    railway variables set ANTHROPIC_API_KEY "$ANTHROPIC_API_KEY"
fi

if [ ! -z "$GOOGLE_AI_API_KEY" ]; then
    railway variables set GOOGLE_AI_API_KEY "$GOOGLE_AI_API_KEY"
fi

echo ""
echo "🔌 Setting up Socket.io server environment..."

# Set socket server variables
railway service use socket-server

railway variables set NODE_ENV production
railway variables set NEXTAUTH_URL "https://$MAIN_DOMAIN"

echo ""
echo "✅ Environment variables configured successfully!"
echo ""
echo "🔍 Next steps:"
echo "1. Run: ./deploy-railway.sh"
echo "2. Check logs: railway logs"
echo "3. Test your applications:"
echo "   - Main App: https://$MAIN_DOMAIN"
echo "   - Socket Server: https://$SOCKET_DOMAIN/health"
